from fastapi import APIRouter
from fastapi import Depends
from fastapi import HTT<PERSON>Exception
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from onyx.db.user_teams import get_user_teams
from onyx.db.user_teams import create_user_teams
from onyx.db.user_teams import prepare_user_teams_for_deletion
from onyx.db.user_teams import update_user_teams
from onyx.server.user_teams.models import UserTeams
from onyx.server.user_teams.models import CreateUserTeams
from onyx.server.user_teams.models import UpdateUserTeams
from onyx.auth.users import current_admin_user
from onyx.auth.users import current_curator_or_admin_user
from onyx.db.engine import get_session
from onyx.db.models import User
from onyx.db.models import UserRole
from onyx.utils.logger import setup_logger

logger = setup_logger()

router = APIRouter(prefix="/manage")


@router.get("/user-teams")
def list_user_teams(
    user: User | None = Depends(current_curator_or_admin_user),
    db_session: Session = Depends(get_session),
    only_up_to_date: bool = False
) -> list[UserTeams]:
    if user is None or user.role == UserRole.ADMIN:
        user_teams = get_user_teams(db_session, only_up_to_date=only_up_to_date)

    return [UserTeams.from_model(user_team) for user_team in user_teams]

@router.post("/user-teams")
def create_user_team(
    user_team: CreateUserTeams,
    _: User | None = Depends(current_admin_user),
    db_session: Session = Depends(get_session),
) -> UserTeams:
    try:
        db_user_team = create_user_teams(db_session, user_team)
    except IntegrityError:
        raise HTTPException(
            400,
            f"User team with name '{user_team.name}' already exists. Please "
            + "choose a different name.",
        )
    return UserTeams.from_model(db_user_team)


@router.patch("/user-teams/{user_team_id}")
def patch_user_teams(
    user_team_id: int,
    user_team_update: UpdateUserTeams,
    user: User | None = Depends(current_curator_or_admin_user),
    db_session: Session = Depends(get_session),
) -> UserTeams:
    try:
        return UserTeams.from_model(
            update_user_teams(
                db_session=db_session,
                user=user,
                user_team_id=user_team_id,
                user_team_update=user_team_update,
            )
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.delete("/user-teams/{user_team_id}")
def delete_user_teams(
    user_team_id: int,
    _: User | None = Depends(current_admin_user),
    db_session: Session = Depends(get_session),
) -> None:
    try:
        prepare_user_teams_for_deletion(db_session, user_team_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))