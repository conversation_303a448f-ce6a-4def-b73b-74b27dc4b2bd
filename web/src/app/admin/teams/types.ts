import { UserRole } from "@/lib/types"; // ✅ reuse if needed

export interface Team {
  id: number;                 // unique team ID
  name: string;               // team name
  is_up_to_date: boolean;     // team status
  users: TeamUser[];          // list of users in this team
}

export interface TeamArgs {
  name: string;               // required when creating/updating
  user_ids: string[];         // required list of user IDs to add to the team
}

// Reuse a minimal user structure for the nested `users` array:
export interface TeamUser {
  id: string;
  email: string;
  role: UserRole;
}

// For user selection in forms
export interface MinimalUserSnapshot {
  id: string;
  email: string;
}
