"use client";

import { Form, Formik } from "formik";
import { PopupSpec } from "@/components/admin/connectors/Popup";
import {
  TextFormField,
} from "@/components/admin/connectors/Field";
import { createTeam, updateTeam } from "./lib";
import { Modal } from "@/components/Modal";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import Text from "@/components/ui/text";
import { Team, MinimalUserSnapshot } from "./types";
import { SearchMultiSelectDropdown } from "@/components/Dropdown";
import useSWR from "swr";
import { useState } from "react";
import { FiX } from "react-icons/fi";

interface TeamFormProps {
  onClose: () => void;
  setPopup: (popupSpec: PopupSpec | null) => void;
  onCreateTeam: (team: Team) => void;
  team?: Team;
}

export const TeamForm = ({
  onClose,
  setPopup,
  onCreateTeam,
  team,
}: TeamFormProps) => {
  const isUpdate = team !== undefined;
  const [selectedUsers, setSelectedUsers] = useState<MinimalUserSnapshot[]>(
    team?.users.map(u => ({ id: u.id, email: u.email })) || []
  );

  // Fetch all users for selection
  const { data: allUsers, error: usersError } = useSWR<MinimalUserSnapshot[]>(
    "/api/users",
    (url: string) => fetch(url).then((r) => r.json())
  );

  return (
    <Modal onOutsideClick={onClose} width="w-2/6">
      <>
        <h2 className="text-xl font-bold flex">
          {isUpdate ? "Update Team" : "Create a new Team"}
        </h2>

        <Separator />

        <Formik
          initialValues={{
            name: team?.name || "",
          }}
          onSubmit={async (values, formikHelpers) => {
            formikHelpers.setSubmitting(true);

            const payload = {
              ...values,
              user_ids: selectedUsers.map(user => user.id)
            };

            let response;
            if (isUpdate) {
              response = await updateTeam(team.id, payload);
            } else {
              response = await createTeam(payload);
            }

            formikHelpers.setSubmitting(false);

            if (response.ok) {
              setPopup({
                message: isUpdate
                  ? "Successfully updated team!"
                  : "Successfully created team!",
                type: "success",
              });

              if (!isUpdate) {
                onCreateTeam(await response.json());
              }

              onClose();
            } else {
              const responseJson = await response.json();
              const errorMsg = responseJson.detail || responseJson.message;
              setPopup({
                message: isUpdate
                  ? `Error updating team - ${errorMsg}`
                  : `Error creating team - ${errorMsg}`,
                type: "error",
              });
            }
          }}
        >
          {({ isSubmitting }) => (
            <Form className="w-full overflow-visible">
              <Text className="mb-4 text-lg">
                Enter a name for your team and select users to add.
              </Text>

              <TextFormField
                name="name"
                label="Team Name:"
                autoCompleteDisabled={true}
              />

              <div className="mb-4">
                <Text className="mb-2 font-medium">Select Users:</Text>

                {/* Display selected users */}
                {selectedUsers.length > 0 && (
                  <div className="mb-3">
                    <Text className="text-sm text-gray-600 mb-2">Selected users:</Text>
                    <div className="flex flex-wrap gap-2">
                      {selectedUsers.map((user) => (
                        <div
                          key={user.id}
                          className="flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm"
                        >
                          <span>{user.email}</span>
                          <button
                            type="button"
                            onClick={() => {
                              setSelectedUsers(selectedUsers.filter(u => u.id !== user.id));
                            }}
                            className="ml-2 text-blue-600 hover:text-blue-800"
                          >
                            <FiX size={14} />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* User selection dropdown */}
                {allUsers && (
                  <SearchMultiSelectDropdown
                    options={allUsers
                      .filter(user => !selectedUsers.some(su => su.id === user.id))
                      .map(user => ({
                        name: user.email,
                        value: user.id,
                      }))}
                    onSelect={(option) => {
                      const user = allUsers.find(u => u.id === option.value);
                      if (user) {
                        setSelectedUsers([...selectedUsers, user]);
                      }
                    }}
                    itemComponent={({ option }) => (
                      <div className="flex items-center px-4 py-2.5 cursor-pointer hover:bg-background-100">
                        <span className="flex-grow">{option.name}</span>
                      </div>
                    )}
                  />
                )}

                {usersError && (
                  <Text className="text-red-500 text-sm mt-2">
                    Error loading users. Please try again.
                  </Text>
                )}
              </div>

              <Button
                type="submit"
                size="sm"
                variant="submit"
                disabled={isSubmitting}
              >
                {isUpdate ? "Update!" : "Create!"}
              </Button>
            </Form>
          )}
        </Formik>
      </>
    </Modal>
  );
};
